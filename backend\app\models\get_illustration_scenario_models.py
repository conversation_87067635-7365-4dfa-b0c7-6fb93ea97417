
from datetime import date
from typing import List, Literal, Optional
from pydantic import BaseModel, Field

class GetIllustrationScenarioRequest(BaseModel):
    policy_id:int
    scenario_id:int
class IllustrationOptionOut(BaseModel):
    option_id: Optional[int]=Field(None, description="Option_id if it exixts")  # ✅ Accepts both int and None
    option_description: str

class IllustrationQuestionOut(BaseModel):
    question_id: int
    question_description: str
    options: List[IllustrationOptionOut]

class IllustrationTypeOut(BaseModel):
    type_id: int
    type_description: str
    questions: List[IllustrationQuestionOut]

class IllustrationObject(BaseModel):
    policy_id:int
    scenario_id:int
    illustration_options: List[IllustrationTypeOut]

class GetIllustrationScenarioResponse(BaseModel):
    illustration:List[IllustrationObject]
    policy_id:int
    scenario_id:int
    illustration_id:int
    date_of_illustration:date
    illustration_type_id:int
    illustration_question_id:int
    illustration_option_id:Optional[int]
    illustration_starting_age:int
    illustration_ending_age:int
    new_face_amount:float
    new_coverage_option:str
    new_premium_amount:float
    new_loan_amount:float
    new_loan_repayment_amount:float
    current_interest_rate:float
    guaranteed_minimum_rate:float
    illustration_interest_rate:float
    surrrender_amount:float
    is_schedule: Optional[Literal['YES', 'NO']] = Field(default='NO')

# New models for get_policy_scenarios endpoint
class ScenarioSummary(BaseModel):
    scenario_id: int
    policy_id: int
    illustration_id: int
    date_of_illustration: date
    illustration_type_id: int
    illustration_type_description: Optional[str] = None
    illustration_question_id: int
    illustration_question_description: Optional[str] = None
    illustration_option_id: Optional[int] = None
    illustration_option_description: Optional[str] = None
    illustration_starting_age: int
    illustration_ending_age: int
    new_face_amount: Optional[float] = None
    new_coverage_option: Optional[str] = None
    new_premium_amount: Optional[float] = None
    new_loan_amount: Optional[float] = None
    new_loan_repayment_amount: Optional[float] = None
    current_interest_rate: Optional[float] = None
    guaranteed_minimum_rate: Optional[float] = None
    illustration_interest_rate: Optional[float] = None
    surrender_amount: Optional[float] = None
    retirement_age_goal: Optional[int] = None
    is_schedule: Optional[Literal['YES', 'NO']] = Field(default='NO')

class GetPolicyScenariosResponse(BaseModel):
    policy_id: int
    scenarios: List[ScenarioSummary]
    total_count: int
