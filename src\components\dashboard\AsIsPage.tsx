import React, { useState, useEffect } from 'react';
import { Calculator, Save } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Notification from '../common/Notification';
import { useDashboard } from '../../contexts/DashboardContext';
import { saveAsIsIllustration, validateAsIsData } from '../../services/asIsService';

const AsIsPage: React.FC = () => {
  const [policyData, setPolicyData] = useState({
    policyNumber: '',
    customerName: '',
    customerId: '',
    policyType: '',
    faceAmount: '',
    annualPremium: '',
    paymentPeriod: '',
    dividendOption: '',
    currentAge: '',
    retirementAge: '',
    lifeExpectancy: '',
  });

  // As-Is Illustration Scenarios state
  const initialIllustrationScenarios = {
    retirementGoalAge: '65',
  };
  const [illustrationScenarios, setIllustrationScenarios] = useState(initialIllustrationScenarios);


  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const { setActiveTab, selectedCustomerData, selectedPolicyData, addScenario } = useDashboard();

  // Populate form data from selected policy and customer data
  useEffect(() => {
    if (selectedCustomerData && selectedPolicyData) {
      // Extract premium amount from string (e.g., "2000 $ annually" -> "2000")
      const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
      const premiumAmount = premiumMatch ? premiumMatch[1] : '';

      // Extract coverage amount from string (e.g., "500,000 $" -> "500000")
      const coverageMatch = selectedPolicyData.coverage.replace(/,/g, '').match(/(\d+)/);
      const coverageAmount = coverageMatch ? coverageMatch[1] : '';

      // Calculate current age from DOB (assuming DOB format is DD.MM.YYYY)
      const calculateAge = (dobString: string) => {
        try {
          if (!dobString || typeof dobString !== 'string') {
            console.warn('Invalid DOB string:', dobString);
            return '30'; // Default fallback age
          }

          const parts = dobString.split('.');
          if (parts.length !== 3) {
            console.warn('DOB format should be DD.MM.YYYY:', dobString);
            return '30'; // Default fallback age
          }

          const [day, month, year] = parts.map(Number);

          // Validate the date parts
          if (isNaN(day) || isNaN(month) || isNaN(year) ||
              day < 1 || day > 31 || month < 1 || month > 12 ||
              year < 1900 || year > new Date().getFullYear()) {
            console.warn('Invalid date parts in DOB:', dobString);
            return '30'; // Default fallback age
          }

          // Safari-compatible date parsing - use YYYY/MM/DD format
          const safariSafeDateStr = `${year}/${month}/${day}`;
          const birthDate = new Date(safariSafeDateStr);
          const today = new Date();
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
          }

          // Validate calculated age
          if (age < 0 || age > 150) {
            console.warn('Calculated age is out of range:', age);
            return '30'; // Default fallback age
          }

          return age.toString();
        } catch (error) {
          console.error('Error calculating age from DOB:', dobString, error);
          return '30'; // Default fallback age
        }
      };

      setPolicyData({
        policyNumber: selectedCustomerData.details["Policy Number"] || selectedCustomerData.policyNumber,
        customerName: selectedCustomerData.name,
        customerId: selectedCustomerData.details["Customer ID"] || selectedCustomerData.customerId,
        policyType: selectedPolicyData.name.toLowerCase().replace(/\s+/g, '-'),
        faceAmount: coverageAmount,
        annualPremium: premiumAmount,
        paymentPeriod: '20', // Default value - could be enhanced with actual data
        dividendOption: 'Paid-up Additions', // Default value - could be enhanced with actual data
        currentAge: selectedCustomerData.details?.DOB ? calculateAge(selectedCustomerData.details.DOB) : '30',
        retirementAge: '65', // Default value - could be enhanced based on customer preferences
        lifeExpectancy: '85', // Default value - could be enhanced based on actuarial data
      });

      // Set intelligent defaults for illustration scenarios based on customer age
      const currentAge = selectedCustomerData.details?.DOB ? parseInt(calculateAge(selectedCustomerData.details.DOB)) : 30;
      const defaultRetirementAge = currentAge < 50 ? '65' : currentAge < 60 ? '67' : '70';
      setIllustrationScenarios({
        retirementGoalAge: defaultRetirementAge,
      });
    }
  }, [selectedCustomerData, selectedPolicyData]);



  const handleIllustrationScenarioChange = (field: string, value: string | boolean) => {
    setIllustrationScenarios(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    // Use the new validation service
    const configData = {
      policyData,
      illustrationScenarios,
      timestamp: new Date().toISOString()
    };

    console.log('🔍 Form data being validated:', {
      policyData,
      illustrationScenarios,
      currentAge: policyData.currentAge,
      currentAgeType: typeof policyData.currentAge,
      currentAgeIsNaN: isNaN(parseInt(policyData.currentAge)),
      selectedCustomerData,
      selectedPolicyData
    });

    return validateAsIsData(configData);
  };

  const handleSaveAsIs = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      setNotification({ message: 'Please fix the following errors:\n' + errors.join('\n'), type: 'error' });
      return;
    }
    setIsSaving(true);
    try {
      // Prepare AS-IS configuration data
      const configData = {
        policyData,
        illustrationScenarios,
        timestamp: new Date().toISOString()
      };

      console.log('Saving AS-IS configuration to database:', configData);

      // Save to database using the new service
      const saveResult = await saveAsIsIllustration(configData);

      if (saveResult.success) {
        setNotification({ message: saveResult.message, type: 'success' });

        // ✅ Add AS-IS scenario to current session for immediate display (maintains fresh start)
        try {
          const retirementAge = illustrationScenarios.retirementGoalAge;
          const scenarioData = {
            id: `as-is-${Date.now()}`,
            name: `As-Is Analysis - Retirement Goal Age: ${retirementAge}`,
            policyId: selectedPolicyData?.id?.toString() || '',
            asIsDetails: `As-Is Analysis - Retirement Goal Age: ${retirementAge}`,
            category: 'as-is' as const,
            keyPoints: [
              `Type: As-Is Analysis`,
              `Question: Retirement Goal Age`,
              `Selected Option: Age ${retirementAge}`,
            ],
            whatIfOptions: [`Retirement Goal Age: ${retirementAge}`],
            data: {
              retirementGoalAge: retirementAge,
              scenarioType: 'as_is_analysis'
            },
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await addScenario(scenarioData);
          setNotification({ message: 'As-Is scenario saved successfully!', type: 'success' });
          console.log('✅ AS-IS scenario added to current session');
        } catch (error) {
          console.error('❌ Error adding AS-IS scenario to session:', error);
          setNotification({ message: 'Error saving As-Is scenario', type: 'error' });
        }
      } else {
        setNotification({ message: saveResult.message, type: 'error' });
      }
    } catch (error) {
      setNotification({ message: 'Error saving AS-IS configuration. Please try again.', type: 'error' });
      console.error('Error saving AS-IS scenario:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetScenarios = () => {
    setIllustrationScenarios(initialIllustrationScenarios);
  };





  return (
    <div className="space-y-6">
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 border-yellow-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800">No Policy Selected</h3>
              <p className="text-yellow-700">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the AS-IS illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Description Section */}
          <Card className="bg-blue-50 border-blue-200">
            <div className="p-6">
              <p className="text-lg text-gray-800 leading-relaxed">
                This scenario shows how your policy will perform if you make no changes and continue as currently structured.
                It reflects the most recent assumptions using the Current Interest Rate, without altering premiums, withdrawals, or face amount.
              </p>
            </div>
          </Card>

          {/* As-Is Illustration Scenarios */}
          <Card>
            <div className="space-y-6">
              {/* Project the current policy As-Is till retirement goal age */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h4 className="text-lg font-semibold text-gray-900 mb-6">
                  1. Project the current policy As-Is till retirement goal age.
                </h4>

                {/* Age Selection Toggle Bar */}
                <div className="mb-4">
                  <label className="block text-lg font-semibold text-gray-800 mb-3">
                    Retirement Goal Age:
                  </label>

                  {/* Compact Age Toggle Control */}
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                      <button
                        type="button"
                        onClick={() => {
                          const currentAge = parseInt(illustrationScenarios.retirementGoalAge);
                          if (currentAge > 25) {
                            handleIllustrationScenarioChange('retirementGoalAge', (currentAge - 1).toString());
                          }
                        }}
                        className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                      >
                        ◀
                      </button>
                      <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                        {illustrationScenarios.retirementGoalAge}
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          const currentAge = parseInt(illustrationScenarios.retirementGoalAge);
                          if (currentAge < 125) {
                            handleIllustrationScenarioChange('retirementGoalAge', (currentAge + 1).toString());
                          }
                        }}
                        className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                      >
                        ▶
                      </button>
                    </div>
                    <span className="text-lg text-gray-600">years</span>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={handleSaveAsIs}
              variant="primary"
              loading={isSaving}
              disabled={isSaving}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save AS-IS Illustration</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <span>Reset Scenarios</span>
            </Button>

          </div>


        </>
      )}
    </div>
  );
};

export default AsIsPage;