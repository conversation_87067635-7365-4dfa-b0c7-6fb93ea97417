from fastapi import HTTPException
from app.db.connection import get_connection
from fastapi import APIRouter
from app.models.get_illustration_scenario_models import (
    GetIllustrationScenarioResponse,
    IllustrationObject,
    IllustrationTypeOut,
    IllustrationQuestionOut,
    IllustrationOptionOut
)
from datetime import datetime, date
from collections import defaultdict

calculation_router = APIRouter()


def fetch_full_policy_details(policy_id: int) -> dict:
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 📌 Main query to fetch policy + related entities
        cursor.execute("""
            SELECT 
    -- POLICY
    p.POLICY_ID, p.CUSTOMER_ID, p.INSURANCE_COMPANY_ID, p.AGENT_ID, p.INSURANCE_PRODUCT_CODE,
    p.POLICY_TYPE, p.POLICY_ISSUED_DATE, p.POLICY_EXPIRY_DATE, p.POLICY_STATUS,
    p.PREMIUM_AMOUNT, p.FACE_AMOUNT, p.LOAN_AMOUNT_DISBURESED,
    p.MINIMUM_INTEREST_RATE_IN_PERCENTAGE, p.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE,
    p.CURRENT_INTEREST_RATE_IN_PERCENTAGE, p.WITHDRAWL_AMOUNT, p.RIDER_APPLICABLE,
    p.POLICY_TERM_YEARS, p.CURRENT_CASH_VALUE,

    -- CUSTOMER
    c.CUSTOMER_FIRST_NAME, c.CUSTOMER_MIDDLE_NAME, c.CUSTOMER_LAST_NAME,
    c.SALUTATION, c.GENDER, c.DATE_OF_BIRTH, c.CONTACT_NUMBER, c.EMAIL,
    c.ADDRESS_LINE_1 AS CUSTOMER_ADDRESS_LINE_1, c.ADDRESS_LINE_2 AS CUSTOMER_ADDRESS_LINE_2,
    c.CITY AS CUSTOMER_CITY, c.STATE AS CUSTOMER_STATE, c.ZIP_CODE AS CUSTOMER_ZIP,
    c.COUNTRY AS CUSTOMER_COUNTRY,

    -- AGENT
    a.AGENT_ID, a.AGENT_CODE, a.SALUTATION AS AGENT_SALUTATION,
    a.AGENT_FIRST_NAME, a.AGENT_MIDDLE_NAME, a.AGENT_LAST_NAME, a.AGENT_NAME,
    a.AGENT_GENDER, a.AGENT_EMAIL, a.AGENT_PHONE_NUMBER,
    a.AGENT_STATE, a.AGENT_STATUS,

    -- PRODUCT
    pr.INSURANCE_PRODUCT_ID, pr.INSURANCE_PRODUCT_CODE, pr.INSURANCE_PRODUCT_NAME,
    pr.INSURANCE_PRODUCT_LINE_OF_BUSINESS, pr.INSURANCE_PRODUCT_DESCRIPTION,
    pr.INSURANCE_PRODUCT_STATUS, pr.INSURANCE_PRODUCT_EXPIRY_DATE,

    -- COMPANY
    ic.INSURANCE_COMPANY_NAME,
    ic.CONTACT_NUMBER AS COMPANY_PHONE, ic.EMAIL AS COMPANY_EMAIL,
    ic.ADDRESS_LINE_1 AS COMPANY_ADDRESS_LINE_1, ic.ADDRESS_LINE_2 AS COMPANY_ADDRESS_LINE_2,
    ic.CITY AS COMPANY_CITY, ic.STATE AS COMPANY_STATE, ic.ZIP_CODE AS COMPANY_ZIP,
    ic.COUNTRY AS COMPANY_COUNTRY

FROM INS_POLICY p
JOIN INS_CUSTOMER c ON p.CUSTOMER_ID = c.CUSTOMER_ID
JOIN INS_INSURANCE_AGENT a ON p.AGENT_ID = a.AGENT_ID
JOIN INS_INSURANCE_PRODUCT pr ON p.INSURANCE_PRODUCT_CODE = pr.INSURANCE_PRODUCT_CODE
JOIN INS_INSURANCE_COMPANY ic ON p.INSURANCE_COMPANY_ID = ic.INSURANCE_COMPANY_ID
WHERE p.POLICY_ID = %s;
        """, (policy_id,))
        policy_data = cursor.fetchone()

        if not policy_data:
            raise Exception("Policy not found.")

        # 🔄 Fetch nested data
        def fetch_related(query, params):
            cursor.execute(query, params)
            return cursor.fetchall()

        policy_data["riders"] = fetch_related("SELECT * FROM INS_RIDER WHERE POLICY_ID = %s", (policy_id,))
        policy_data["beneficiaries"] = fetch_related("SELECT * FROM INS_BENEFICIARY WHERE POLICY_ID = %s", (policy_id,))
        policy_data["transactions"] = fetch_related("SELECT * FROM INS_TRANSACTION WHERE POLICY_ID = %s", (policy_id,))
        policy_data["loans"] = []

        cursor.execute("SELECT * FROM INS_LOAN WHERE POLICY_ID = %s", (policy_id,))
        loans = cursor.fetchall()

        for loan in loans:
            cursor.execute("SELECT * FROM INS_SCHEDULE_REPAYMENT WHERE LOAN_ID = %s", (loan["LOAN_ID"],))
            loan["repayments"] = cursor.fetchall()
            policy_data["loans"].append(loan)

        return policy_data

    finally:
        cursor.close()
        conn.close()

def get_illustration_scenario_service(policy_id: int, scenario_id: int) -> GetIllustrationScenarioResponse:
    connection = get_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        print(f"DEBUG: Fetching scenario for POLICY_ID={policy_id}, SCENARIO_ID={scenario_id}")

        # Step 1: Fetch scenario data with date from ILLUSTRATION_TABLE
        cursor.execute("""
            SELECT s.*, i.DATE_OF_ILLUSTRATION
            FROM ILLUSTRATION_SCENARIO_TABLE s
            LEFT JOIN ILLUSTRATION_TABLE i ON s.ILLUSTRATION_ID = i.ILLUSTRATION_ID
            WHERE s.POLICY_ID = %s AND s.SCENARIO_ID = %s
        """, (policy_id, scenario_id))

        scenario_row = cursor.fetchone()
        print("DEBUG: Scenario Row =", scenario_row)

        if not scenario_row:
            raise HTTPException(status_code=404, detail="No scenario found")

        # Extract IDs
        type_id = scenario_row["ILLUSTRATION_TYPE_ID"]
        question_id = scenario_row["ILLUSTRATION_QUESTION_ID"]
        option_id = scenario_row["ILLUSTRATION_OPTION_ID"]

        print(f"DEBUG: type_id={type_id}, question_id={question_id}, option_id={option_id}")

        # Step 2: Fetch type description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_TYPE_TABLE
            WHERE ILLUSTRATION_TYPE_ID = %s
        """, (type_id,))
        type_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Type Description =", type_description)

        # Step 3: Fetch question description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_QUESTION_TABLE
            WHERE ILLUSTRATION_QUESTION_ID = %s
        """, (question_id,))
        question_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Question Description =", question_description)

        # Step 4: Clean and validate option_id
        invalid_option_values = {None, 0, "0", "null", "None"}
        option_id_str = str(option_id).strip()
        clean_option_id = None if option_id_str in invalid_option_values else int(option_id)

        print(f"DEBUG: Cleaned Option ID = {clean_option_id} (type: {type(clean_option_id)})")

        # Step 4: Fetch option description safely
        if clean_option_id is not None:
            cursor.execute("""
                SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
                WHERE ILLUSTRATION_OPTION_ID = %s
            """, (clean_option_id,))
            option_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        else:
            option_description = {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Option Description =", option_description)

        # Step 5: Prepare the nested structure
        option_obj = IllustrationOptionOut(
            option_id=clean_option_id,
            option_description=option_description["SHORT_DESCRIPTION"]
        )

        print("DEBUG: Created option_obj =", option_obj)

        question_obj = IllustrationQuestionOut(
            question_id=question_id,
            question_description=question_description["SHORT_DESCRIPTION"],
            options=[option_obj]
        )

        type_obj = IllustrationTypeOut(
            type_id=type_id,
            type_description=type_description["SHORT_DESCRIPTION"],
            questions=[question_obj]
        )

        illustration_object = IllustrationObject(
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_options=[type_obj]
        )

        # Step 6: Build final response
        response = GetIllustrationScenarioResponse(
            illustration=[illustration_object],
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_id=scenario_row["ILLUSTRATION_ID"],
            date_of_illustration=scenario_row["DATE_OF_ILLUSTRATION"],
            illustration_type_id=scenario_row["ILLUSTRATION_TYPE_ID"],
            illustration_question_id=scenario_row["ILLUSTRATION_QUESTION_ID"],
            illustration_option_id=scenario_row["ILLUSTRATION_OPTION_ID"],
            illustration_starting_age=scenario_row["ILLUSTRATION_STARTING_AGE"] or 0,
            illustration_ending_age=scenario_row["ILLUSTRATION_ENDING_AGE"] or 0,
            new_face_amount=scenario_row["NEW_FACE_AMOUNT"] or 0.0,
            new_coverage_option=scenario_row["NEW_COVERAGE_OPTION"] or "",
            new_premium_amount=scenario_row["NEW_PREMIUM_AMOUNT"] or 0.0,
            new_loan_amount=scenario_row["NEW_LOAN_AMOUNT"] or 0.0,
            new_loan_repayment_amount=scenario_row["NEW_LOAN_REPAYMENT_AMOUNT"] or 0.0,
            current_interest_rate=scenario_row["CURRENT_INTEREST_RATE"] or 0.0,
            guaranteed_minimum_rate=scenario_row["GUARANTEED_INTEREST_RATE"] or 0.0,
            illustration_interest_rate=scenario_row["ILLUSTRATION_INTEREST_RATE"] or 0.0,
            surrrender_amount=scenario_row.get("SURRENDER_AMOUNT") or 0.0,
            retirement_age_goal=scenario_row.get("RETIREMENT_AGE_GOAL") or None,
            is_schedule=scenario_row["SCHEDULE"]
        )

        print("DEBUG: Final Response =", response)
        return response

    finally:
        cursor.close()
        connection.close()


'''def get_policy_scenarios_service(policy_id: int) -> GetPolicyScenariosResponse:
    """
    Retrieve all scenarios for a specific policy ID
    """
    connection = get_main_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        print(f"DEBUG: Fetching all scenarios for POLICY_ID={policy_id}")

        # Step 1: Fetch all scenarios for the policy with type and question descriptions
        cursor.execute("""
            SELECT
                s.*,
                i.DATE_OF_ILLUSTRATION,
                t.SHORT_DESCRIPTION as type_description,
                q.SHORT_DESCRIPTION as question_description
            FROM ILLUSTRATION_SCENARIO_TABLE s
            LEFT JOIN ILLUSTRATION_TABLE i ON s.ILLUSTRATION_ID = i.ILLUSTRATION_ID
            LEFT JOIN ILLUSTRATION_TYPE_TABLE t ON s.ILLUSTRATION_TYPE_ID = t.ILLUSTRATION_TYPE_ID
            LEFT JOIN ILLUSTRATION_QUESTION_TABLE q ON s.ILLUSTRATION_QUESTION_ID = q.ILLUSTRATION_QUESTION_ID
            WHERE s.POLICY_ID = %s
            ORDER BY s.SCENARIO_ID DESC
        """, (policy_id,))

        scenario_rows = cursor.fetchall()
        print(f"DEBUG: Found {len(scenario_rows)} scenarios for policy {policy_id}")

        scenarios = []
        for row in scenario_rows:
            # Handle option description if option_id exists
            option_description = None
            if row["ILLUSTRATION_OPTION_ID"]:
                cursor.execute("""
                    SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
                    WHERE ILLUSTRATION_OPTION_ID = %s
                """, (row["ILLUSTRATION_OPTION_ID"],))
                option_result = cursor.fetchone()
                if option_result:
                    option_description = option_result["SHORT_DESCRIPTION"]

            scenario = GetIllustrationScenarioResponse(
                scenario_id=row["SCENARIO_ID"],
                policy_id=row["POLICY_ID"],
                illustration_id=row["ILLUSTRATION_ID"],
                date_of_illustration=row["DATE_OF_ILLUSTRATION"],
                illustration_type_id=row["ILLUSTRATION_TYPE_ID"],
                illustration_type_description=row["type_description"] or "",
                illustration_question_id=row["ILLUSTRATION_QUESTION_ID"],
                illustration_question_description=row["question_description"] or "",
                illustration_option_id=row["ILLUSTRATION_OPTION_ID"],
                illustration_option_description=option_description,
                illustration_starting_age=row["ILLUSTRATION_STARTING_AGE"],
                illustration_ending_age=row["ILLUSTRATION_ENDING_AGE"],
                new_face_amount=row["NEW_FACE_AMOUNT"],
                new_coverage_option=row["NEW_COVERAGE_OPTION"],
                new_premium_amount=row["NEW_PREMIUM_AMOUNT"],
                new_loan_amount=row["NEW_LOAN_AMOUNT"],
                new_loan_repayment_amount=row["NEW_LOAN_REPAYMENT_AMOUNT"],
                current_interest_rate=row["CURRENT_INTEREST_RATE"],
                guaranteed_minimum_rate=row["GUARANTEED_INTEREST_RATE"],
                illustration_interest_rate=row["ILLUSTRATION_INTEREST_RATE"],
                surrender_amount=row["SURRENDER_AMOUNT"],
                retirement_age_goal=row["RETIREMENT_AGE_GOAL"],
                is_schedule=row["SCHEDULE"]
            )
            scenarios.append(scenario)

        response = GetPolicyScenariosResponse(
            policy_id=policy_id,
            scenarios=scenarios,
            total_count=len(scenarios)
        )

        print(f"DEBUG: Returning {len(scenarios)} scenarios for policy {policy_id}")
        return response

    except Exception as e:
        print(f"ERROR: Failed to fetch scenarios for policy {policy_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch scenarios: {str(e)}")

    finally:
        cursor.close()
        connection.close()'''

def get_year_from_date(dt):
    if isinstance(dt, datetime):
        return dt.year
    elif isinstance(dt, date):
        return dt.year
    elif isinstance(dt, str):
        return datetime.strptime(dt, "%Y-%m-%d").year
    else:
        raise ValueError("Unsupported date format")

def to_float(val):
    if val is None:
        return 0.0
    try:
        return float(val)
    except Exception:
        return 0.0

def initialize_data(policy_id: int, scenario_id: int):
    policy_detail = fetch_full_policy_details(policy_id)
    print("&&&&&&&&&&&&&&&&",policy_detail)
    scenario_details = get_illustration_scenario_service(policy_id, scenario_id)

    print("$$$$$$$$",scenario_details)
    scenario_dict = scenario_details.dict()

    illustration = scenario_dict.get("illustration", [])
    

    if illustration:
        type_id = illustration[0]["illustration_options"][0]["type_id"]
        question_id = illustration[0]["illustration_options"][0]["questions"][0]["question_id"]
        option_id = illustration[0]["illustration_options"][0]["questions"][0]["options"][0]["option_id"]
    else:
        type_id = question_id = option_id = None

    print("###########",scenario_dict.get('new_face_amount'))
    # type_id = scenario_details.illustration[0].illustration_options[0].type_id
    # question_id = scenario_details.illustration[0].illustration_options[0].questions[0].question_id
    # option_id=scenario_details.illustration[0].illustration_options[0].questions[0].options[0].option_id
    policy_issued_date = policy_detail.get("POLICY_ISSUED_DATE")
    dob = policy_detail.get("DATE_OF_BIRTH")
    # retirement_age_goal = scenario_details.retirement_age_goal
    retirement_age_goal = scenario_details.retirement_age_goal  
    if retirement_age_goal is None:
        retirement_age_goal =100
    else:
        retirement_age_goal = int(retirement_age_goal)

    current_year = datetime.now().year
    policy_issued_year = get_year_from_date(policy_issued_date)
    dob_year = get_year_from_date(dob)

    policy_starting_year = current_year- policy_issued_year  + 1
    customer_current_age = current_year - dob_year
    policy_current_year = current_year - policy_issued_year + 1
    policy_ending_year = policy_current_year + (retirement_age_goal - customer_current_age)
    n_years = policy_ending_year + 1

    planned_premium = [0.0] * n_years
    interest_rate = [0.0] * n_years
    face_amount = [0.0] * n_years
    policy_loan = [0.0] * n_years
    loan_repayment = [0.0] * n_years
    charges = [0.0] * n_years
    withdrawal = [0.0] * n_years
    loan_interest_rate = [0.0] * n_years
    initial_loan_outstanding = [0.0] * n_years
    ages = [0] * n_years
    policy_years = [0] * n_years
    base_cost_for_age = [0.0] * n_years
    annual_increase = [0.0] * n_years
    base_face_amount = [0.0] * n_years

    initial_net_cash_value = to_float(policy_detail.get("CURRENT_CASH_VALUE"))
    initial_loan_outstanding_val = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
    # initial_loan_outstanding_val = 0.0
    # Build a mapping: year -> total loan repayment for that year
    loan_repayment_by_year = defaultdict(float)
    for loan in policy_detail.get("loans", []):
        loan_interest_value = to_float(loan.get("LOAN_INTEREST_RATE_IN_PERCENTAGE", 0.0)) / 100.0
        for repayment in loan.get("repayments", []):
            repayment_amount = to_float(repayment.get("LOAN_REPAYMENT_AMOUNT"))
            # due_date = repayment.get("LOAN_SCHEDULED_DUE_DATE")
            # print("!!!!!!!!!!!!!!!",repayment.get("LOAN_REPAYMENT_AMOUNT"))
            # if due_date:
            #     year = due_date.year if hasattr(due_date, "year") else datetime.strptime(due_date, "%Y-%m-%d").year
            #     loan_repayment_by_year[year] += float(repayment.get("LOAN_REPAYMENT_AMOUNT", 0.0))

    if type_id == 1 and question_id == 101: #ASIS 1
        for i in range(policy_starting_year, policy_ending_year + 1):
            policy_years[i] = i
            ages[i] = customer_current_age + (i - policy_current_year)
            if ages[i] > retirement_age_goal:
                    break
            planned_premium[i] = to_float(policy_detail.get("PREMIUM_AMOUNT"))
            interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
            face_amount[i] = to_float(policy_detail.get("FACE_AMOUNT"))
                # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
            policy_loan[i] = 0.0
            loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                # Set loan_repayment[i] based on the current year
            year = current_year - policy_current_year + i
                # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
            loan_repayment[i] = repayment_amount
            charges[i] = 100
                # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
            withdrawal[i] = 0.0
            base_cost_for_age[i] = 300
            annual_increase[i] = 0.0
            base_face_amount[i] = face_amount[i]
    elif type_id == 2 and question_id == 201:
        if option_id ==20101: #face amount changes 2
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(policy_detail.get("PREMIUM_AMOUNT"))
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
                print('values@@@@@@@@ for s2',face_amount[i], planned_premium[i], interest_rate[i], loan_interest_rate[i], loan_repayment[i])

        elif option_id == 20102: #modified face amount by year s3
            face_amount_by_year = scenario_details.new_face_amount  # need to give shedule logic here 
            default_face_amount = to_float(policy_detail.get("FACE_AMOUNT"))

            for i in range(policy_starting_year, policy_ending_year + 1):
                    policy_years[i] = i
                    ages[i] = customer_current_age + (i - policy_current_year)
                    if ages[i] > retirement_age_goal:
                        break
                    planned_premium[i] = to_float(policy_detail.get("PREMIUM_AMOUNT"))
                    interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    face_amount[i] = to_float(face_amount_by_year.get(i, default_face_amount))
                        # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                        # Set loan_repayment[i] based on the current year
                    year = current_year - policy_current_year + i
                        # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                        # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                    withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]
    elif type_id == 2  and question_id == 202:
        switch_age = scenario_details.illustration_starting_age
        if option_id == 20202:  #change to option b
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(policy_detail.get("PREMIUM_AMOUNT"))
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
                cash_value = calculate_net_cash_value = [0.0] * n_years
                death_benefit = [0.0] * n_years
                print('cash_value',cash_value)
                print('cash_value',cash_value[i])
                if i == policy_starting_year:
                    cash_value[i] = 0.0
                else:
                    cash_value[i] = cash_value[i - 1]

                # add premium
                cash_value[i] += planned_premium[i]

                # grow with interest
                cash_value[i] += cash_value[i] * interest_rate[i]

                # deduct charges/withdrawals
                cash_value[i] -= charges[i]
                cash_value[i] -= withdrawal[i]

                # --- DEATH BENEFIT LOGIC ---
                if ages[i] < switch_age:
                    death_benefit[i] = face_amount[i]  # Option A: level
                else:
                    death_benefit[i] = face_amount[i] + cash_value[i]  # Option B: increasing

    elif type_id == 2 and question_id == 203:
        if option_id == 20302:  # Modify to Option A later
            if retirement_age_goal is not None:
                modification_year = int(retirement_age_goal)
        else:
            # Fallback: switch in 5 years if not set
            modification_year = customer_current_age + 5

        for i in range(policy_starting_year, policy_ending_year + 1):
            policy_years[i] = i
            ages[i] = customer_current_age + (i - policy_current_year)

            if ages[i] > retirement_age_goal:
                break  # fallback to retirement
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                
                if ages[i] > retirement_age_goal:
                    break
                
                planned_premium[i] = to_float(policy_detail.get("PREMIUM_AMOUNT"))
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                
                # Apply old face amount before modification year, new face amount after
                if ages[i] < modification_year:
                    face_amount[i] = to_float(policy_detail.get("FACE_AMOUNT"))
                else:
                    face_amount[i] = to_float(scenario_details.new_face_amount)
                
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0

                    year = current_year - policy_current_year + i
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                    withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]
                    cash_value = calculate_net_cash_value = [0.0] * n_years
                    death_benefit = [0.0] * n_years

                    if i == policy_starting_year:
                        cash_value[i] = 0.0
                    else:
                        cash_value[i] = cash_value[i - 1]

                        cash_value[i] += planned_premium[i]                       # Add premium
                        cash_value[i] += cash_value[i] * interest_rate[i]         # Apply interest
                        cash_value[i] -= charges[i]                               # Deduct charges
                        cash_value[i] -= withdrawal[i]                            # Deduct withdrawals

            # ---- Death Benefit Calculation ----
                        if ages[i] < modification_year:
                            # Option B: increasing
                            death_benefit[i] = face_amount[i] + cash_value[i]
                        else:
                            # Option A: level
                            death_benefit[i] = face_amount[i]
            
    elif type_id == 3 and question_id == 301:
        if option_id == 30101: #premium amount change s4
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(policy_detail.get("FACE_AMOUNT"))
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
                print('values@@@@@@@@',face_amount[i], planned_premium[i], interest_rate[i], loan_interest_rate[i], loan_repayment[i])
        if option_id == 30102: #lump sum premium same as premium logic
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(policy_detail.get("FACE_AMOUNT"))
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
        if option_id == 30103: #(shedule logic)
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
                
    elif type_id == 3 and question_id == 302:   
        if option_id == 30202:
             #premium payment stops 
            premium_payment_term = int(policy_detail.get("PREMIUM_PAYMENT_TERM", 0))
            premium_stop_age = customer_current_age + premium_payment_term

            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                        break
                if ages[i] < premium_stop_age:
                    planned_premium[i] = to_float(scenario_details.new_premium_amount)
                else:
                    planned_premium[i] = 0.0

                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(policy_detail.get("FACE_AMOUNT"))
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
    elif type_id == 3 and question_id == 303:
    # Step 1: Setup initial values
        current_cash_value = to_float(policy_detail.get("CURRENT_CASH_VALUE", 0.0))
        years_to_goal = retirement_age_goal - customer_current_age
        avg_interest_rate = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
        target_cash_value = current_cash_value * ((1 + avg_interest_rate) ** years_to_goal)
    
   

    # Step 3: Loop over projection years
        for i in range(policy_starting_year, policy_ending_year + 1):
            policy_years[i] = i
            ages[i] = customer_current_age + (i - policy_current_year)

            if ages[i] > retirement_age_goal:
                break

            
            planned_premium[i] = to_float(scenario_details.new_premium_amount)
            interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
            face_amount[i] = to_float(scenario_details.new_face_amount)
            policy_loan[i] = 0.0
            loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE", 0.0)) / 100.0
            loan_repayment[i] = repayment_amount
            charges[i] = 100
            withdrawal[i] = 0.0
            base_cost_for_age[i] = 300
            annual_increase[i] = 0.0
            base_face_amount[i] = face_amount[i]

        # Step 6: Calculate cash value
 #       if i == policy_starting_year:
 #           cash_value[i] = current_cash_value
 #       else:
 #           cash_value[i] = cash_value[i - 1]

 #       cash_value[i] += planned_premium[i]  # add premium
 #       cash_value[i] += cash_value[i] * interest_rate[i]  # grow with interest
 #       cash_value[i] -= charges[i]  # deduct charges
 #       cash_value[i] -= withdrawal[i]  # deduct withdrawals

        # Step 7: Check if target is reached
 #       if premium_active and cash_value[i] >= target_cash_value:
#           premium_active = False  # stop premiums from now on

    elif type_id == 4 and question_id == 401:
            if option_id == 40103: #'Stress scenario rate: _____%', 'STRESS SCENARIO RATE'
                stress_rate = to_float(scenario_details.illustration_interest_rate) / 100.0

            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)

                if ages[i] > retirement_age_goal:
                    break

                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = stress_rate  # Apply stress scenario rate
                face_amount[i] = to_float(scenario_details.new_face_amount)

                policy_loan[i] = 0.0
                loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0

                year = current_year - policy_current_year + i
                loan_repayment[i] = repayment_amount

                charges[i] = 100
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]

                # Calculate cash value for this year
                if i == policy_starting_year:
                    cash_value[i] = current_cash_value
                else:
                    cash_value[i] = cash_value[i - 1]

                # Add premium
                cash_value[i] += planned_premium[i]
                # Grow with interest
                cash_value[i] += cash_value[i] * interest_rate[i]
                # Deduct charges and withdrawal
                cash_value[i] -= charges[i]
                cash_value[i] -= withdrawal[i]

                # Stop premiums when target cash value is reached
                if cash_value[i] >= target_cash_value:
                    # You can add logic here to handle stopping premiums if needed
                    pass 

    if option_id == 40104:  #User-defined Rates (shedule logic)
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]

    elif type_id == 5 and question_id == 501:
            if option_id == 50101: #INCOME BY FULL SURRENDER
                surrender_amount = to_float(scenario_details.surrrender_amount)  # from DB column

            # Determine the surrender age — can be ending age in the scenario
                surrender_age = scenario_details.illustration_ending_age

                surrender_done = False

                for i in range(policy_starting_year, policy_ending_year + 1):
                    policy_years[i] = i
                    ages[i] = customer_current_age + (i - policy_current_year)
                    if ages[i] > retirement_age_goal:
                        break
                    if surrender_done:
                    # After surrender, everything becomes 0
                        planned_premium[i] = 0.0
                        interest_rate[i] = 0.0
                        face_amount[i] = 0.0
                        policy_loan[i] = 0.0
                        loan_interest_rate[i] = 0.0
                        loan_repayment[i] = 0.0
                        charges[i] = 0.0
                        withdrawal[i] = 0.0
                        base_cost_for_age[i] = 0.0
                        annual_increase[i] = 0.0
                        base_face_amount[i] = 0.0
                        continue
                    planned_premium[i] = to_float(policy_detail.get("PREMIUM_AMOUNT"))
                    interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    face_amount[i] = to_float(policy_detail.get("FACE_AMOUNT"))
                        # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                        # Set loan_repayment[i] based on the current year
                    year = current_year - policy_current_year + i
                        # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                        # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))   
                    withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]
            
            # Cash value growth before surrender
            if i == policy_starting_year:
                cash_value[i] = to_float(policy_detail.get("CURRENT_CASH_VALUE", 0.0))
            else:
                cash_value[i] = cash_value[i - 1] + planned_premium[i]
                cash_value[i] += cash_value[i] * interest_rate[i]
                cash_value[i] -= charges[i]
                cash_value[i] -= withdrawal[i]

            # Check if we reached surrender age
            if ages[i] == surrender_age:
                # Income is the net cash value at surrender
                surrender_income = cash_value[i] - policy_loan[i]
                scenario_details.surrrender_amount = surrender_income
                surrender_done = True



    if option_id == 50102:  #MODIFY FULL SURRENDER BY INCOME
            surrender_amount = to_float(scenario_details.surrrender_amount)  # from DB column
            surrender_age = scenario_details.illustration_starting_age  # or wherever surrender starts
            end_age = scenario_details.illustration_ending_age

            income_years = max(1, end_age - surrender_age + 1)
            annual_income = surrender_amount / income_years

            surrender_done = False
            remaining_balance = surrender_amount

            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                if surrender_done:
            # After surrender starts, we just pay income until balance is gone
                    planned_premium[i] = 0.0
                    interest_rate[i] = 0.0
                    face_amount[i] = 0.0
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = 0.0
                    loan_repayment[i] = 0.0
                    charges[i] = 0.0
                    withdrawal[i] = min(annual_income, remaining_balance)
                    base_cost_for_age[i] = 0.0
                    annual_increase[i] = 0.0
                    base_face_amount[i] = 0.0
                    cash_value[i] = remaining_balance - withdrawal[i]
                    remaining_balance -= withdrawal[i]
                    continue

                # Before surrender, normal setup
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                policy_loan[i] = 0.0
                loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                year = current_year - policy_current_year + i
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]

                # Start surrender income at the specified age
            if ages[i] == surrender_age:
                surrender_done = True
                cash_value[i] = remaining_balance

                
    elif type_id == 5 and question_id == 502:
            if option_id == 50201: #Fixed amount: $__________ or % of available cash value', 'FIXED AMOUNT OR AVAILABLE CASH VALUE
                policy_withdrawal_amount = to_float(policy_detail.get("WITHDRAWL_AMOUNT", 0.0))
                current_cash_value = to_float(policy_detail.get("CURRENT_CASH_VALUE", 0.0))

            if current_cash_value > 0:
                withdrawal_percentage = (policy_withdrawal_amount / current_cash_value) * 100
            else:
                withdrawal_percentage = 0.0

                for i in range(policy_starting_year, policy_ending_year + 1):
                    policy_years[i] = i
                    ages[i] = customer_current_age + (i - policy_current_year)
                    if ages[i] > retirement_age_goal:
                        break
                    planned_premium[i] = to_float(scenario_details.new_premium_amount)
                    interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    face_amount[i] = to_float(scenario_details.new_face_amount)
                        # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                        # Set loan_repayment[i] based on the current year
                    year = current_year - policy_current_year + i
                        # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                    withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                    # withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]
                    net_cash_value = calculate_net_cash_value = [0.0] * n_years

                    if withdrawal_percentage > 0 and net_cash_value.get(i, 0.0) > 0:
                        withdrawal[i] = (net_cash_value[i] * withdrawal_percentage) / 100.0
                else:
                    withdrawal[i] = policy_withdrawal_amount  # fallback to fixed amount
                    
            if option_id == 50202: #INCREASING INCOME STREAM
                withdrawal_start = to_float(policy_detail.get("WITHDRAWL_AMOUNT", 0.0))
        # Growth rate = Current interest rate (in decimal form)
                withdrawal_growth_rate = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE", 0.0)) / 100.0
                for i in range(policy_starting_year, policy_ending_year + 1):
                    policy_years[i] = i
                    ages[i] = customer_current_age + (i - policy_current_year)
                    if ages[i] > retirement_age_goal:
                        break
                    planned_premium[i] = to_float(scenario_details.new_premium_amount)
                    interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    face_amount[i] = to_float(scenario_details.new_face_amount)
                        # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                        # Set loan_repayment[i] based on the current year
                    year = current_year - policy_current_year + i
                        # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                        # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                    withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]
                    net_cash_value = calculate_net_cash_value = [0.0] * n_years
                    net_cash_value_start = calculate_net_cash_value[policy_starting_year]
                    

                    years_since_start = i - policy_starting_year
                    withdrawal[i] = withdrawal_start * ((1 + withdrawal_growth_rate) ** years_since_start)

            # Withdrawal percentage of net value start
            if net_cash_value_start[i] > 0:
                withdrawal_percentage[i] = round((withdrawal[i] / net_cash_value_start[i]) * 100.0, 2)
            else:
                withdrawal_percentage[i] = 0.0

            base_cost_for_age[i] = 300
            annual_increase[i] = 0.0
            base_face_amount[i] = face_amount[i]

    if option_id == 50203: #FLAT ANNUAL AMOUNT
            flat_amount = to_float(policy_detail.get("WITHDRAWL_AMOUNT", 0.0))
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = flat_amount
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
                '''if net_value_start.get(i, 0.0) > 0:
                    withdrawal_percentage[i] = round((withdrawal[i] / net_value_start[i]) * 100.0, 2)
        else:
            withdrawal_percentage[i] = 0.0'''

                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
    if option_id == 50204: #shedule logic
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
    if type_id == 5 and question_id == 503:
        if option_id == 50301: #'Fixed amount or % of available cash value
            fixed_withdrawal = to_float(policy_detail.get("WITHDRAWAL_AMOUNT", 0.0))
            cash_value_amount = to_float(policy_detail.get("CASH_VALUE", 0.0))
          
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                if fixed_withdrawal > 0:
                    withdrawal[i] = fixed_withdrawal
                else:
                    withdrawal[i] = (cash_value_amount * fixed_withdrawal) / 100.0 if cash_value_amount > 0 else 0.0
                
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]
        if option_id == 50303: #ANNAUL AMOUNT
            annual_withdrawal = to_float(policy_detail.get("WITHDRAWAL_AMOUNT", 0.0)) 
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                withdrawal[i] = annual_withdrawal
                yearly_cash_value = to_float(policy_detail.get("CURRENT_CASH_VALUE", 0.0))
        if yearly_cash_value > 0:
            withdrawal_percentage[i] = round((withdrawal[i] / yearly_cash_value) * 100.0, 2)
        else:
            withdrawal_percentage[i] = 0.0

        base_cost_for_age[i] = 300
        annual_increase[i] = 0.0
        base_face_amount[i] = face_amount[i]
                # Set loan_repayment[i] based on the current year
        if option_id == 50304: #SCHEDULE LOGIC
            for i in range(policy_starting_year, policy_ending_year + 1):
                policy_years[i] = i
                ages[i] = customer_current_age + (i - policy_current_year)
                if ages[i] > retirement_age_goal:
                    break
                planned_premium[i] = to_float(scenario_details.new_premium_amount)
                interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                face_amount[i] = to_float(scenario_details.new_face_amount)
                    # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                policy_loan[i] = 0.0
                loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    # Set loan_repayment[i] based on the current year
                year = current_year - policy_current_year + i
                    # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                loan_repayment[i] = repayment_amount
                charges[i] = 100
                    # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                withdrawal[i] = 0.0
                base_cost_for_age[i] = 300
                annual_increase[i] = 0.0
                base_face_amount[i] = face_amount[i]

        if type_id == 6 and question_id == 60101:
            if option_id == 60101:  # Change to New Loan Repayment amount now
                for i in range(policy_starting_year, policy_ending_year + 1):
                    policy_years[i] = i
                    ages[i] = customer_current_age + (i - policy_current_year)
                    if ages[i] > retirement_age_goal:
                        break
                    planned_premium[i] = to_float(scenario_details.new_premium_amount)
                    interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    face_amount[i] = to_float(scenario_details.new_face_amount)
                    loan_repayment[i] = to_float(scenario_details.new_loan_repayment_amount)    
                        # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                        # Set loan_repayment[i] based on the current year
                    year = current_year - policy_current_year + i
                        # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                        # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                    withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]
            if option_id == 60102:  # shedule logic
                for i in range(policy_starting_year, policy_ending_year + 1):
                    policy_years[i] = i
                    ages[i] = customer_current_age + (i - policy_current_year)
                    if ages[i] > retirement_age_goal:
                        break
                    planned_premium[i] = to_float(scenario_details.new_premium_amount)
                    interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    face_amount[i] = to_float(scenario_details.new_face_amount)
                        # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                        # Set loan_repayment[i] based on the current year
                    year = current_year - policy_current_year + i
                        # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                        # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                    withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]

            elif option_id == 60103:  # ONE-TIME PREMIUM
                for i in range(policy_starting_year, policy_ending_year + 1):
                    policy_years[i] = i
                    ages[i] = customer_current_age + (i - policy_current_year)
                    if ages[i] > retirement_age_goal:
                        break
                    planned_premium[i] = to_float(scenario_details.new_premium_amount)
                    interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                    face_amount[i] = to_float(policy_detail.get("FACE_AMOUNT")) 
                        # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
                    policy_loan[i] = 0.0
                    loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
                        # Set loan_repayment[i] based on the current year
                    year = current_year - policy_current_year + i
                        # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
                    loan_repayment[i] = repayment_amount
                    charges[i] = 100
                        # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
                    withdrawal[i] = 0.0
                    base_cost_for_age[i] = 300
                    annual_increase[i] = 0.0
                    base_face_amount[i] = face_amount[i]
       
    # if type_id == 2 and question_id == 201:
    #     for i in range(policy_starting_year, policy_ending_year + 1):
    #         policy_years[i] = i
    #         ages[i] = customer_current_age + (i - policy_current_year)
    #         if ages[i] > retirement_age_goal:
    #             break
    #         planned_premium[i] = to_float(policy_detail.get("PREMIUM_AMOUNT"))
    #         interest_rate[i] = to_float(policy_detail.get("GUARANTEED_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
    #         face_amount[i] = to_float(scenario_details.get("NEW_FACE_AMOUNT"))
    #         # policy_loan[i] = to_float(policy_detail.get("LOAN_AMOUNT_DISBURESED"))
    #         policy_loan[i] = 0.0
    #         loan_interest_rate[i] = loan_interest_rate[i] = to_float(policy_detail.get("CURRENT_INTEREST_RATE_IN_PERCENTAGE")) / 100.0
    #         # Set loan_repayment[i] based on the current year
    #         year = current_year - policy_current_year + i
    #         # loan_repayment[i] = loan_repayment_by_year.get(year, 0.0)
    #         loan_repayment[i] = repayment_amount
    #         charges[i] = 100
    #         # withdrawal[i] = to_float(policy_detail.get("WITHDRAWL_AMOUNT"))
    #         withdrawal[i] = 0.0
    #         base_cost_for_age[i] = 300
    #         annual_increase[i] = 0.0
    #         base_face_amount[i] = face_amount[i]
    

    return {
        "policy_years": policy_years,
        "ages": ages,
        "planned_premium": planned_premium,
        "interest_rate": interest_rate,
        "face_amount": face_amount,
        "policy_loan": policy_loan,
        "loan_interest_rate": loan_interest_rate,
        "initial_loan_outstanding": initial_loan_outstanding,
        "loan_repayment": loan_repayment,
        "charges": charges,
        "withdrawal": withdrawal,
        "policy_starting_year": policy_starting_year,
        "policy_ending_year": policy_ending_year,
        "initial_net_cash_value": initial_net_cash_value,
        "initial_loan_outstanding_val": initial_loan_outstanding_val,
        "base_face_amount": base_face_amount,
        "base_cost_for_age": base_cost_for_age,
        "annual_increase": annual_increase,
    }