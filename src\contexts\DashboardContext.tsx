import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Policy, PolicySearchFormData, Scenario, DashboardState, SelectedCustomerData, SelectedPolicyData } from '../types';
import { fetchAllowedIllustrationTypes, IllustrationTypesRequest } from '../services/illustrationService';
import { fetchPolicyScenarios } from '../services/scenarioService';

interface DashboardContextType extends DashboardState {
  setActiveTab: (tab: string) => void;
  setCurrentPolicy: (policy: Policy | null) => void;
  setSelectedCustomerData: (customerData: SelectedCustomerData | null) => void;
  setSelectedPolicyData: (policyData: SelectedPolicyData | null) => void;
  setPolicySearchFormData: (formData: PolicySearchFormData | null) => void;
  addScenario: (scenario: Scenario) => Promise<string | null>;
  updateScenario: (id: string, updates: Partial<Scenario>) => Promise<void>;
  deleteScenario: (id: string) => Promise<void>;
  deleteMultipleScenarios: (ids: string[]) => Promise<void>;
  toggleScenarioSelection: (id: string) => void;
  selectAllScenarios: (select: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setAllowedIllustrationTypes: (typeIds: number[]) => void;
  fetchAllowedIllustrationTypes: (policyId: number, customerId: number, policyType?: string) => Promise<void>;
  loadScenariosFromBackend: (policyId: number) => Promise<void>;
  refreshScenarios: () => Promise<void>;
  clearAllData: () => void; // New function to clear all data
  resetDashboard: () => void; // Reset dashboard to fresh state
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

// Storage keys for localStorage
const STORAGE_KEYS = {
  DASHBOARD_STATE: 'insuranceApp_dashboardState',
  ACTIVE_TAB: 'insuranceApp_activeTab',
  CURRENT_POLICY: 'insuranceApp_currentPolicy',
  SCENARIOS: 'insuranceApp_scenarios',
  SELECTED_SCENARIOS: 'insuranceApp_selectedScenarios',
  SELECTED_CUSTOMER_DATA: 'insuranceApp_selectedCustomerData',
  SELECTED_POLICY_DATA: 'insuranceApp_selectedPolicyData',
  POLICY_SEARCH_FORM_DATA: 'insuranceApp_policySearchFormData',
  ALLOWED_ILLUSTRATION_TYPES: 'insuranceApp_allowedIllustrationTypes'
};

// Helper functions for localStorage operations
const saveToStorage = (key: string, data: any) => {
  try {
    if (data !== null && data !== undefined) {
      localStorage.setItem(key, JSON.stringify(data));
    } else {
      localStorage.removeItem(key);
    }
  } catch (error) {
    console.warn(`Failed to save ${key} to localStorage:`, error);
  }
};

const loadFromStorage = (key: string, defaultValue: any = null) => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch (error) {
    console.warn(`Failed to load ${key} from localStorage:`, error);
    return defaultValue;
  }
};

// Load initial state with proper fresh login vs refresh detection
const getInitialState = (): DashboardState => {
  const isAuthenticated = localStorage.getItem('insuranceApp_user');

  if (!isAuthenticated) {
    // Not authenticated - return clean state
    console.log('❌ No authentication - clean state');
    return {
      activeTab: 'policy-selection',
      currentPolicy: null,
      scenarios: [],
      selectedScenarios: [],
      selectedCustomerData: null,
      selectedPolicyData: null,
      policySearchFormData: null,
      loading: false,
      error: null,
      allowedIllustrationTypes: [],
    };
  }

  // Check if ANY dashboard data exists in localStorage
  const hasAnyDashboardData = Object.values(STORAGE_KEYS).some(key =>
    localStorage.getItem(key) !== null
  );

  if (!hasAnyDashboardData) {
    // Fresh login - no dashboard data exists at all
    console.log('🆕 Fresh login detected - no dashboard data exists, starting clean');
    return {
      activeTab: 'policy-selection',
      currentPolicy: null,
      scenarios: [],
      selectedScenarios: [],
      selectedCustomerData: null,
      selectedPolicyData: null,
      policySearchFormData: null,
      loading: false,
      error: null,
      allowedIllustrationTypes: [],
    };
  } else {
    // Page refresh - dashboard data exists, load it
    console.log('🔄 Page refresh detected - dashboard data exists, loading it');
    return {
      activeTab: loadFromStorage(STORAGE_KEYS.ACTIVE_TAB, 'policy-selection'),
      currentPolicy: loadFromStorage(STORAGE_KEYS.CURRENT_POLICY, null),
      scenarios: loadFromStorage(STORAGE_KEYS.SCENARIOS, []),
      selectedScenarios: loadFromStorage(STORAGE_KEYS.SELECTED_SCENARIOS, []),
      selectedCustomerData: loadFromStorage(STORAGE_KEYS.SELECTED_CUSTOMER_DATA, null),
      selectedPolicyData: loadFromStorage(STORAGE_KEYS.SELECTED_POLICY_DATA, null),
      policySearchFormData: loadFromStorage(STORAGE_KEYS.POLICY_SEARCH_FORM_DATA, null),
      loading: false,
      error: null,
      allowedIllustrationTypes: loadFromStorage(STORAGE_KEYS.ALLOWED_ILLUSTRATION_TYPES, []),
    };
  }
};

type DashboardAction =
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_CURRENT_POLICY'; payload: Policy | null }
  | { type: 'SET_SELECTED_CUSTOMER_DATA'; payload: SelectedCustomerData | null }
  | { type: 'SET_SELECTED_POLICY_DATA'; payload: SelectedPolicyData | null }
  | { type: 'SET_POLICY_SEARCH_FORM_DATA'; payload: PolicySearchFormData | null }
  | { type: 'ADD_SCENARIO'; payload: Scenario }
  | { type: 'SET_SCENARIOS'; payload: Scenario[] }
  | { type: 'UPDATE_SCENARIO'; payload: { id: string; updates: Partial<Scenario> } }
  | { type: 'DELETE_SCENARIO'; payload: string }
  | { type: 'DELETE_MULTIPLE_SCENARIOS'; payload: string[] }
  | { type: 'TOGGLE_SCENARIO_SELECTION'; payload: string }
  | { type: 'SET_SELECTED_SCENARIOS'; payload: string[] }
  | { type: 'SELECT_ALL_SCENARIOS'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ALLOWED_ILLUSTRATION_TYPES'; payload: number[] }
  | { type: 'RESET_DASHBOARD' };

const dashboardReducer = (state: DashboardState, action: DashboardAction): DashboardState => {
  let newState: DashboardState;

  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      newState = { ...state, activeTab: action.payload };
      saveToStorage(STORAGE_KEYS.ACTIVE_TAB, action.payload);
      return newState;
    case 'SET_CURRENT_POLICY':
      newState = { ...state, currentPolicy: action.payload };
      saveToStorage(STORAGE_KEYS.CURRENT_POLICY, action.payload);
      return newState;
    case 'SET_SELECTED_CUSTOMER_DATA':
      newState = { ...state, selectedCustomerData: action.payload };
      saveToStorage(STORAGE_KEYS.SELECTED_CUSTOMER_DATA, action.payload);
      return newState;
    case 'SET_SELECTED_POLICY_DATA':
      newState = { ...state, selectedPolicyData: action.payload };
      saveToStorage(STORAGE_KEYS.SELECTED_POLICY_DATA, action.payload);
      return newState;
    case 'SET_POLICY_SEARCH_FORM_DATA':
      newState = { ...state, policySearchFormData: action.payload };
      saveToStorage(STORAGE_KEYS.POLICY_SEARCH_FORM_DATA, action.payload);
      return newState;
    case 'ADD_SCENARIO':
      newState = { ...state, scenarios: [...state.scenarios, action.payload] };
      saveToStorage(STORAGE_KEYS.SCENARIOS, newState.scenarios);
      return newState;
    case 'SET_SCENARIOS':
      newState = { ...state, scenarios: action.payload };
      saveToStorage(STORAGE_KEYS.SCENARIOS, action.payload);
      return newState;
    case 'UPDATE_SCENARIO':
      newState = {
        ...state,
        scenarios: state.scenarios.map(scenario =>
          scenario.id === action.payload.id
            ? { ...scenario, ...action.payload.updates }
            : scenario
        ),
      };
      saveToStorage(STORAGE_KEYS.SCENARIOS, newState.scenarios);
      return newState;
    case 'DELETE_SCENARIO':
      newState = {
        ...state,
        scenarios: state.scenarios.filter(scenario => scenario.id !== action.payload),
        selectedScenarios: state.selectedScenarios.filter(id => id !== action.payload),
      };
      saveToStorage(STORAGE_KEYS.SCENARIOS, newState.scenarios);
      saveToStorage(STORAGE_KEYS.SELECTED_SCENARIOS, newState.selectedScenarios);
      return newState;
    case 'DELETE_MULTIPLE_SCENARIOS':
      newState = {
        ...state,
        scenarios: state.scenarios.filter(scenario => !action.payload.includes(scenario.id)),
        selectedScenarios: [],
      };
      saveToStorage(STORAGE_KEYS.SCENARIOS, newState.scenarios);
      saveToStorage(STORAGE_KEYS.SELECTED_SCENARIOS, newState.selectedScenarios);
      return newState;
    case 'TOGGLE_SCENARIO_SELECTION':
      newState = {
        ...state,
        selectedScenarios: state.selectedScenarios.includes(action.payload)
          ? state.selectedScenarios.filter(id => id !== action.payload)
          : [...state.selectedScenarios, action.payload],
      };
      saveToStorage(STORAGE_KEYS.SELECTED_SCENARIOS, newState.selectedScenarios);
      return newState;
    case 'SET_SELECTED_SCENARIOS':
      newState = {
        ...state,
        selectedScenarios: action.payload,
      };
      saveToStorage(STORAGE_KEYS.SELECTED_SCENARIOS, action.payload);
      return newState;
    case 'SELECT_ALL_SCENARIOS':
      newState = {
        ...state,
        selectedScenarios: action.payload ? state.scenarios.map(s => s.id) : [],
      };
      saveToStorage(STORAGE_KEYS.SELECTED_SCENARIOS, newState.selectedScenarios);
      return newState;
    case 'SET_LOADING':
      // Don't persist loading state
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      // Don't persist error state
      return { ...state, error: action.payload };
    case 'SET_ALLOWED_ILLUSTRATION_TYPES':
      newState = { ...state, allowedIllustrationTypes: action.payload };
      saveToStorage(STORAGE_KEYS.ALLOWED_ILLUSTRATION_TYPES, action.payload);
      return newState;
    case 'RESET_DASHBOARD':
      console.log('🔄 Resetting dashboard to fresh state');
      // Clear all localStorage data
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      // Return fresh state
      return {
        activeTab: 'policy-selection',
        currentPolicy: null,
        scenarios: [],
        selectedScenarios: [],
        selectedCustomerData: null,
        selectedPolicyData: null,
        policySearchFormData: null,
        loading: false,
        error: null,
        allowedIllustrationTypes: [],
      };
    default:
      return state;
  }
};

// Function to clear all stored data
const clearAllStoredData = () => {
  Object.values(STORAGE_KEYS).forEach(key => {
    localStorage.removeItem(key);
  });
};

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Always get fresh initial state when component mounts
  const [state, dispatch] = useReducer(dashboardReducer, getInitialState());

  // Initialize with proper state detection
  useEffect(() => {
    const initializeData = async () => {
      // Check if this is a fresh login or page refresh
      const isAuthenticated = localStorage.getItem('insuranceApp_user');

      if (!isAuthenticated) {
        console.log('❌ No authentication - initializing clean state');
        dispatch({ type: 'RESET_DASHBOARD' });
        return;
      }

      // Check if ANY dashboard data exists in localStorage
      const hasAnyDashboardData = Object.values(STORAGE_KEYS).some(key =>
        localStorage.getItem(key) !== null
      );

      if (!hasAnyDashboardData) {
        console.log('🆕 Fresh login detected - forcing clean dashboard state');
        dispatch({ type: 'RESET_DASHBOARD' });
      } else {
        console.log('🔄 Page refresh detected - dashboard data exists, keeping current state');
      }

      dispatch({ type: 'SET_LOADING', payload: false });
      dispatch({ type: 'SET_ERROR', payload: null });
      console.log('Dashboard state:', {
        activeTab: state.activeTab,
        hasCurrentPolicy: !!state.currentPolicy,
        scenariosCount: state.scenarios.length,
        hasSelectedCustomerData: !!state.selectedCustomerData,
        hasSelectedPolicyData: !!state.selectedPolicyData,
        hasPolicySearchFormData: !!state.policySearchFormData,
        allowedIllustrationTypesCount: state.allowedIllustrationTypes.length
      });
    };

    initializeData();
  }, []);

  // No localStorage auto-save - all data is managed through backend

  return (
    <DashboardContext.Provider value={{
      ...state,
      setActiveTab: (tab: string) => dispatch({ type: 'SET_ACTIVE_TAB', payload: tab }),
      setCurrentPolicy: (policy: Policy | null) => dispatch({ type: 'SET_CURRENT_POLICY', payload: policy }),
      setSelectedCustomerData: (customerData: SelectedCustomerData | null) => dispatch({ type: 'SET_SELECTED_CUSTOMER_DATA', payload: customerData }),
      setSelectedPolicyData: (policyData: SelectedPolicyData | null) => dispatch({ type: 'SET_SELECTED_POLICY_DATA', payload: policyData }),
      setPolicySearchFormData: (formData: PolicySearchFormData | null) => dispatch({ type: 'SET_POLICY_SEARCH_FORM_DATA', payload: formData }),
      addScenario: async (scenario: Scenario) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });

          // Add the scenario to current session state for immediate display
          // This maintains fresh start (no old scenarios) but shows current session scenarios
          console.log('✅ Adding scenario to current session:', scenario.name);

          // Generate a unique ID for the current session
          const sessionScenarioId = 'session-' + Date.now().toString();
          const sessionScenario = {
            ...scenario,
            id: sessionScenarioId
          };

          // Add to current session scenarios (immediate display)
          dispatch({ type: 'ADD_SCENARIO', payload: sessionScenario });

          console.log('✅ Scenario added to current session and will display immediately');
          return sessionScenarioId;
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to create scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to create scenario:', error);
          return null;
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      updateScenario: async (id: string, updates: Partial<Scenario>) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // TODO: Implement backend scenario update API
          console.warn('⚠️ Scenario update not implemented - backend API needed');
          dispatch({ type: 'UPDATE_SCENARIO', payload: { id, updates } });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to update scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to update scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteScenario: async (id: string) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // TODO: Implement backend scenario deletion API
          console.warn('⚠️ Scenario deletion not implemented - backend API needed');
          dispatch({ type: 'DELETE_SCENARIO', payload: id });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to delete scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteMultipleScenarios: async (ids: string[]) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // TODO: Implement backend multiple scenario deletion API
          console.warn('⚠️ Multiple scenario deletion not implemented - backend API needed');
          dispatch({ type: 'DELETE_MULTIPLE_SCENARIOS', payload: ids });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to delete scenarios';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete multiple scenarios:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      toggleScenarioSelection: (id: string) => dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: id }),
      selectAllScenarios: (select: boolean) => dispatch({ type: 'SELECT_ALL_SCENARIOS', payload: select }),
      setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
      setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),
      setAllowedIllustrationTypes: (typeIds: number[]) => dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: typeIds }),
      fetchAllowedIllustrationTypes: async (policyId: number, customerId: number, policyType?: string) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          dispatch({ type: 'SET_ERROR', payload: null });

          const requestData: IllustrationTypesRequest = {
            policyId,
            customerId,
            policyType,
          };

          const response = await fetchAllowedIllustrationTypes(requestData);

          console.log('🔍 API Response:', response.success ? '✅ SUCCESS' : '❌ FAILED');
          console.log('📊 Allowed Type IDs:', response.allowedTypeIds);
          console.log('💬 Message:', response.message);

          if (response.success) {
            // Ensure we have at least some types
            const typeIds = response.allowedTypeIds.length > 0 ? response.allowedTypeIds : [1, 2, 3, 4, 5, 6];
            dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: typeIds });
            console.log('✅ Set allowed illustration types:', typeIds);
          } else {
            console.warn('❌ Backend failed:', response.message);
            dispatch({ type: 'SET_ERROR', payload: response.message || 'Failed to fetch allowed illustration types' });
            // Keep default types on error
            dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: [1, 2, 3, 4, 5, 6] });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch allowed illustration types';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Error fetching allowed illustration types:', error);
          // Keep default types on error
          dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: [1, 2, 3, 4, 5, 6] });
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      loadScenariosFromBackend: async (policyId: number) => {
        console.log('🔍 Loading scenarios from backend for policy ID:', policyId);
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        try {

          // Fetch scenarios from backend
          const scenarios = await fetchPolicyScenarios(policyId);

          console.log('✅ Successfully loaded scenarios from backend:', scenarios.length);

          // Update state with loaded scenarios
          dispatch({ type: 'SET_SCENARIOS', payload: scenarios });
          dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: scenarios });

        } catch (error) {
          console.error('❌ Error loading scenarios from backend:', error);

          // On error, start with empty scenarios but show error
          dispatch({ type: 'SET_SCENARIOS', payload: [] });
          dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: [] });
          dispatch({ type: 'SET_ERROR', payload: `Failed to load scenarios: ${error instanceof Error ? error.message : 'Unknown error'}` });

          // Show user-friendly message
          console.log('⚠️ Starting with empty scenarios due to loading error. User can create new illustrations.');
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      refreshScenarios: async () => {
        // DO NOT REFRESH OLD SCENARIOS - Always keep fresh start
        console.log('🚫 refreshScenarios called but DISABLED for fresh start');
        console.log('🔄 Keeping empty scenarios for fresh illustration experience');

        // Always maintain empty scenarios - no loading from backend
        dispatch({ type: 'SET_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_LOADING', payload: false });

        console.log('✅ Refresh completed: Maintaining fresh start with no old scenarios');
      },
      clearAllData: () => {
        // Clear all dashboard data for fresh start
        dispatch({ type: 'SET_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_SELECTED_POLICY_DATA', payload: null });
        dispatch({ type: 'SET_SELECTED_CUSTOMER_DATA', payload: null });
        dispatch({ type: 'SET_POLICY_SEARCH_FORM_DATA', payload: null });
        dispatch({ type: 'SET_CURRENT_POLICY', payload: null });
        dispatch({ type: 'SET_ACTIVE_TAB', payload: 'policy-selection' });
        dispatch({ type: 'SET_LOADING', payload: false });
        dispatch({ type: 'SET_ERROR', payload: null });

        // Also clear localStorage
        clearAllStoredData();

        console.log('🧹 All dashboard data and localStorage cleared for fresh start');
      },
      resetDashboard: () => {
        console.log('🔄 Resetting dashboard to fresh state after logout');
        dispatch({ type: 'RESET_DASHBOARD' });
      },
    }}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};