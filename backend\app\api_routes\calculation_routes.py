from fastapi import APIRouter, HTTPException
from app.models.Calculation_model import CalculationRequest
from app.services.Calculation_services import (
    calculate_data_from_calculation_engine
)

router = APIRouter(prefix="/api/v1/illustration", tags=["Illustration"])

@router.post("/calculate")
async def calculation_for_selected_option(request: CalculationRequest):
    try:
        result = calculate_data_from_calculation_engine(request.policy_id, request.scenario_id)
        return {"result": result}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))