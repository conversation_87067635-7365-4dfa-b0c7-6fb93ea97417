from fastapi import HTTPException
from app.db.connection import get_connection
from app.models.get_illustration_scenario_models import (
    GetIllustrationScenarioResponse,
    IllustrationObject,
    IllustrationTypeOut,
    IllustrationQuestionOut,
    IllustrationOptionOut,
    ScenarioSummary,
    GetPolicyScenariosResponse
)

# def get_illustration_scenario_service(policy_id: int, scenario_id: int) -> GetIllustrationScenarioResponse:
#     connection = get_connection()
#     cursor = connection.cursor(dictionary=True)

#     try:
#         # Step 1: Fetch scenario data
#         cursor.execute("""
#             SELECT * FROM ILLUSTRATION_SCENARIO_TABLE
#             WHERE POLICY_ID = %s AND SCENARIO_ID = %s
#         """, (policy_id, scenario_id))

#         scenario_row = cursor.fetchone()
#         if not scenario_row:
#             raise HTTPException(status_code=404, detail="No scenario found")

#         # Extract IDs
#         type_id = scenario_row["ILLUSTRATION_TYPE_ID"]
#         question_id = scenario_row["ILLUSTRATION_QUESTION_ID"]
#         option_id = scenario_row["ILLUSTRATION_OPTION_ID"]

#         # Step 2: Fetch type description
#         cursor.execute("""
#             SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_TYPE_TABLE
#             WHERE ILLUSTRATION_TYPE_ID = %s
#         """, (type_id,))
#         type_description = cursor.fetchone()
#         if not type_description:
#             type_description = {"SHORT_DESCRIPTION": ""}

#         # Step 3: Fetch question description
#         cursor.execute("""
#             SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_QUESTION_TABLE
#             WHERE ILLUSTRATION_QUESTION_ID = %s
#         """, (question_id,))
#         question_description = cursor.fetchone()
#         if not question_description:
#             question_description = {"SHORT_DESCRIPTION": ""}

#         # Step 4: Fetch option description
#         cursor.execute("""
#             SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
#             WHERE ILLUSTRATION_OPTION_ID = %s
#         """, (option_id,))
#         option_description = cursor.fetchone()
#         if not option_description:
#             option_description = {"SHORT_DESCRIPTION": ""}

#         # Step 5: Prepare the nested structure
#         option_obj = IllustrationOptionOut(
#             option_id=option_id,
#             option_description=option_description["SHORT_DESCRIPTION"]
#         )

#         question_obj = IllustrationQuestionOut(
#             question_id=question_id,
#             question_description=question_description["SHORT_DESCRIPTION"],
#             options=[option_obj]
#         )

#         type_obj = IllustrationTypeOut(
#             type_id=type_id,
#             type_description=type_description["SHORT_DESCRIPTION"],
#             questions=[question_obj]
#         )

#         illustration_object = IllustrationObject(
#             policy_id=policy_id,
#             scenario_id=scenario_id,
#             illustration_options=[type_obj]
#         )

#         # Step 6: Build final response
#         return GetIllustrationScenarioResponse(
#             illustration=[illustration_object],
#             policy_id=policy_id,
#             scenario_id=scenario_id,
#             illustration_id=scenario_row["ILLUSTRATION_ID"],
#             date_of_illustration=scenario_row["DATE_OF_ILLUSTRATION"],
#             illustration_type_id=scenario_row["ILLUSTRATION_TYPE_ID"],
#             illustration_question_id=scenario_row["ILLUSTRATION_QUESTION_ID"],
#             illustration_option_id=scenario_row["ILLUSTRATION_OPTION_ID"],
#             illustration_starting_age=scenario_row["ILLUSTRATION_STARTING_AGE"],
#             illustration_ending_age=scenario_row["ILLUSTRATION_ENDING_AGE"],
#             new_face_amount=scenario_row["NEW_FACE_AMOUNT"],
#             new_coverage_option=scenario_row["NEW_COVERAGE_OPTION"],
#             new_premium_amount=scenario_row["NEW_PREMIUM_AMOUNT"],
#             new_loan_amount=scenario_row["NEW_LOAN_AMOUNT"],
#             new_loan_repayment_amount=scenario_row["NEW_LOAN_REPAYMENT_AMOUNT"],
#             current_interest_rate=scenario_row["CURRENT_INTEREST_RATE"],
#             guaranteed_minimum_rate=scenario_row["GUARANTEED_INTEREST_RATE"],
#             illustration_interest_rate=scenario_row["ILLUSTRATION_INTEREST_RATE"],
#             surrrender_amount=scenario_row["SURRENDER_AMOUNT"],
#             is_schedule=scenario_row["SCHEDULE"]
#         )
#     except Exception as e:
#         # Log error (optional) and raise meaningful exception
#         raise HTTPException(status_code=500, detail=f"Failed to fetch illustration scenario: {str(e)}")

#     finally:
#         cursor.close()
#         connection.close()
# from fastapi import HTTPException





def get_illustration_scenario_service(policy_id: int, scenario_id: int) -> GetIllustrationScenarioResponse:
    connection = get_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        print(f"DEBUG: Fetching scenario for POLICY_ID={policy_id}, SCENARIO_ID={scenario_id}")

        # Step 1: Fetch scenario data
        cursor.execute("""
            SELECT * FROM ILLUSTRATION_SCENARIO_TABLE
            WHERE POLICY_ID = %s AND SCENARIO_ID = %s
        """, (policy_id, scenario_id))

        scenario_row = cursor.fetchone()
        print("DEBUG: Scenario Row =", scenario_row)

        if not scenario_row:
            raise HTTPException(status_code=404, detail="No scenario found")

        # Extract IDs
        type_id = scenario_row["ILLUSTRATION_TYPE_ID"]
        question_id = scenario_row["ILLUSTRATION_QUESTION_ID"]
        option_id = scenario_row["ILLUSTRATION_OPTION_ID"]

        print(f"DEBUG: type_id={type_id}, question_id={question_id}, option_id={option_id}")

        # Step 2: Fetch type description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_TYPE_TABLE
            WHERE ILLUSTRATION_TYPE_ID = %s
        """, (type_id,))
        type_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Type Description =", type_description)

        # Step 3: Fetch question description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_QUESTION_TABLE
            WHERE ILLUSTRATION_QUESTION_ID = %s
        """, (question_id,))
        question_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Question Description =", question_description)

        # Step 4: Clean and validate option_id
        invalid_option_values = {None, 0, "0", "null", "None"}
        option_id_str = str(option_id).strip()
        clean_option_id = None if option_id_str in invalid_option_values else int(option_id)

        print(f"DEBUG: Cleaned Option ID = {clean_option_id} (type: {type(clean_option_id)})")

        # Step 4: Fetch option description safely
        if clean_option_id is not None:
            cursor.execute("""
                SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
                WHERE ILLUSTRATION_OPTION_ID = %s
            """, (clean_option_id,))
            option_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        else:
            option_description = {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Option Description =", option_description)

        # Step 5: Prepare the nested structure
        option_obj = IllustrationOptionOut(
            option_id=clean_option_id,
            option_description=option_description["SHORT_DESCRIPTION"]
        )

        print("DEBUG: Created option_obj =", option_obj)

        question_obj = IllustrationQuestionOut(
            question_id=question_id,
            question_description=question_description["SHORT_DESCRIPTION"],
            options=[option_obj]
        )

        type_obj = IllustrationTypeOut(
            type_id=type_id,
            type_description=type_description["SHORT_DESCRIPTION"],
            questions=[question_obj]
        )

        illustration_object = IllustrationObject(
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_options=[type_obj]
        )

        # Step 6: Build final response
        response = GetIllustrationScenarioResponse(
            illustration=[illustration_object],
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_id=scenario_row["ILLUSTRATION_ID"],
            date_of_illustration=scenario_row["DATE_OF_ILLUSTRATION"],
            illustration_type_id=scenario_row["ILLUSTRATION_TYPE_ID"],
            illustration_question_id=scenario_row["ILLUSTRATION_QUESTION_ID"],
            illustration_option_id=scenario_row["ILLUSTRATION_OPTION_ID"],
            illustration_starting_age=scenario_row["ILLUSTRATION_STARTING_AGE"],
            illustration_ending_age=scenario_row["ILLUSTRATION_ENDING_AGE"],
            new_face_amount=scenario_row["NEW_FACE_AMOUNT"],
            new_coverage_option=scenario_row["NEW_COVERAGE_OPTION"],
            new_premium_amount=scenario_row["NEW_PREMIUM_AMOUNT"],
            new_loan_amount=scenario_row["NEW_LOAN_AMOUNT"],
            new_loan_repayment_amount=scenario_row["NEW_LOAN_REPAYMENT_AMOUNT"],
            current_interest_rate=scenario_row["CURRENT_INTEREST_RATE"],
            guaranteed_minimum_rate=scenario_row["GUARANTEED_INTEREST_RATE"],
            illustration_interest_rate=scenario_row["ILLUSTRATION_INTEREST_RATE"],
            surrrender_amount=scenario_row["SURRENDER_AMOUNT"],
            is_schedule=scenario_row["SCHEDULE"]
        )

        print("DEBUG: Final Response =", response)
        return response

    finally:
        cursor.close()
        connection.close()


def get_policy_scenarios_service(policy_id: int) -> GetPolicyScenariosResponse:
    """
    Get all scenarios for a specific policy
    """
    connection = get_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        print(f"🔍 Fetching all scenarios for POLICY_ID={policy_id}")

        # Fetch all scenarios for the policy with related descriptions
        # Note: DATE_OF_ILLUSTRATION column doesn't exist in current schema, so we'll use SCENARIO_ID for ordering
        query = """
            SELECT
                ist.SCENARIO_ID,
                ist.POLICY_ID,
                ist.ILLUSTRATION_ID,
                CURDATE() as date_of_illustration,
                ist.ILLUSTRATION_TYPE_ID,
                itt.SHORT_DESCRIPTION as illustration_type_description,
                ist.ILLUSTRATION_QUESTION_ID,
                iqt.SHORT_DESCRIPTION as illustration_question_description,
                ist.ILLUSTRATION_OPTION_ID,
                iot.SHORT_DESCRIPTION as illustration_option_description,
                ist.ILLUSTRATION_STARTING_AGE,
                ist.ILLUSTRATION_ENDING_AGE,
                ist.NEW_FACE_AMOUNT,
                ist.NEW_COVERAGE_OPTION,
                ist.NEW_PREMIUM_AMOUNT,
                ist.NEW_LOAN_AMOUNT,
                ist.NEW_LOAN_REPAYMENT_AMOUNT,
                ist.CURRENT_INTEREST_RATE,
                ist.GUARANTEED_INTEREST_RATE,
                ist.ILLUSTRATION_INTEREST_RATE,
                ist.SURRENDER_AMOUNT,
                ist.RETIREMENT_AGE_GOAL,
                ist.SCHEDULE
            FROM ILLUSTRATION_SCENARIO_TABLE ist
            LEFT JOIN ILLUSTRATION_TYPE_TABLE itt ON ist.ILLUSTRATION_TYPE_ID = itt.ILLUSTRATION_TYPE_ID
            LEFT JOIN ILLUSTRATION_QUESTION_TABLE iqt ON ist.ILLUSTRATION_QUESTION_ID = iqt.ILLUSTRATION_QUESTION_ID
            LEFT JOIN ILLUSTRATION_OPTION_TABLE iot ON ist.ILLUSTRATION_OPTION_ID = iot.ILLUSTRATION_OPTION_ID
            WHERE ist.POLICY_ID = %s
            ORDER BY ist.SCENARIO_ID DESC
        """

        cursor.execute(query, (policy_id,))
        scenario_rows = cursor.fetchall()

        print(f"📊 Found {len(scenario_rows)} scenarios for policy {policy_id}")

        # Convert to ScenarioSummary objects
        scenarios = []
        for row in scenario_rows:
            scenario = ScenarioSummary(
                scenario_id=row['SCENARIO_ID'],
                policy_id=row['POLICY_ID'],
                illustration_id=row['ILLUSTRATION_ID'],
                date_of_illustration=row['date_of_illustration'],
                illustration_type_id=row['ILLUSTRATION_TYPE_ID'],
                illustration_type_description=row.get('illustration_type_description'),
                illustration_question_id=row['ILLUSTRATION_QUESTION_ID'],
                illustration_question_description=row.get('illustration_question_description'),
                illustration_option_id=row.get('ILLUSTRATION_OPTION_ID'),
                illustration_option_description=row.get('illustration_option_description'),
                illustration_starting_age=row.get('ILLUSTRATION_STARTING_AGE') or 0,
                illustration_ending_age=row.get('ILLUSTRATION_ENDING_AGE') or 0,
                new_face_amount=row.get('NEW_FACE_AMOUNT'),
                new_coverage_option=row.get('NEW_COVERAGE_OPTION'),
                new_premium_amount=row.get('NEW_PREMIUM_AMOUNT'),
                new_loan_amount=row.get('NEW_LOAN_AMOUNT'),
                new_loan_repayment_amount=row.get('NEW_LOAN_REPAYMENT_AMOUNT'),
                current_interest_rate=row.get('CURRENT_INTEREST_RATE'),
                guaranteed_minimum_rate=row.get('GUARANTEED_INTEREST_RATE'),
                illustration_interest_rate=row.get('ILLUSTRATION_INTEREST_RATE'),
                surrender_amount=row.get('SURRENDER_AMOUNT'),
                retirement_age_goal=row.get('RETIREMENT_AGE_GOAL'),
                is_schedule=row.get('SCHEDULE', 'NO')
            )
            scenarios.append(scenario)

        response = GetPolicyScenariosResponse(
            policy_id=policy_id,
            scenarios=scenarios,
            total_count=len(scenarios)
        )

        print(f"✅ Successfully retrieved {len(scenarios)} scenarios for policy {policy_id}")
        return response

    except Exception as e:
        print(f"❌ Error fetching policy scenarios: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch policy scenarios: {str(e)}")

    finally:
        cursor.close()
        connection.close()
