from fastapi import APIRouter, HTTPException, Query
from app.models.get_illustration_scenario_models import (
    GetIllustrationScenarioRequest,
    GetIllustrationScenarioResponse,
    GetPolicyScenariosResponse
)
from app.services.get_illustration_scenario_services import get_illustration_scenario_service, get_policy_scenarios_service

get_scenario = APIRouter()

# @get_scenario.post("/get_selected_scenario", response_model=GetIllustrationScenarioResponse)
# def get_illustration_scenario(request: GetIllustrationScenarioRequest):
#     try:
#         return get_illustration_scenario_service(
#             policy_id=request.policy_id,
#             scenario_id=request.scenario_id
#         )
#     except HTTPException as e:
#         raise e
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))
@get_scenario.get("/get_selected_scenario", response_model=GetIllustrationScenarioResponse)
def get_illustration_scenario(
    policy_id: int = Query(...),
    scenario_id: int = Query(...)
):
    try:
        return get_illustration_scenario_service(policy_id, scenario_id)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@get_scenario.get("/get_policy_scenarios", response_model=GetPolicyScenariosResponse)
def get_policy_scenarios(
    policy_id: int = Query(..., description="Policy ID to fetch scenarios for")
):
    """
    Get all scenarios for a specific policy
    """
    try:
        print(f"🔍 API: Fetching all scenarios for policy ID: {policy_id}")
        return get_policy_scenarios_service(policy_id)
    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"❌ API Error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
